"""
Database connection and management utilities
"""

import os
import logging
from typing import Optional
from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError
import mysql.connector
from mysql.connector import Error

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'database': os.getenv('DB_NAME', 'recommendation_db'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', 'rootpassword')
}

# SQLAlchemy setup
DATABASE_URL = f"mysql+mysqlconnector://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}"

engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=False
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()


class DatabaseManager:
    """Database management utilities"""
    
    def __init__(self):
        self.engine = engine
        self.SessionLocal = SessionLocal
    
    def get_db_session(self):
        """Get database session"""
        db = self.SessionLocal()
        try:
            yield db
        finally:
            db.close()
    
    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            connection = mysql.connector.connect(**DB_CONFIG)
            if connection.is_connected():
                logger.info("Successfully connected to MySQL database")
                connection.close()
                return True
        except Error as e:
            logger.error(f"Error connecting to MySQL: {e}")
            return False
    
    def create_database(self) -> bool:
        """Create database if it doesn't exist"""
        try:
            # Connect without specifying database
            config_without_db = DB_CONFIG.copy()
            config_without_db.pop('database', None)
            
            connection = mysql.connector.connect(**config_without_db)
            cursor = connection.cursor()
            
            # Create database
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {DB_CONFIG['database']}")
            logger.info(f"Database {DB_CONFIG['database']} created or already exists")
            
            cursor.close()
            connection.close()
            return True
            
        except Error as e:
            logger.error(f"Error creating database: {e}")
            return False
    
    def execute_sql_file(self, file_path: str) -> bool:
        """Execute SQL file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                sql_script = file.read()
            
            # Split by semicolon and execute each statement
            statements = [stmt.strip() for stmt in sql_script.split(';') if stmt.strip()]
            
            connection = mysql.connector.connect(**DB_CONFIG)
            cursor = connection.cursor()
            
            for statement in statements:
                if statement:
                    cursor.execute(statement)
            
            connection.commit()
            cursor.close()
            connection.close()
            
            logger.info(f"Successfully executed SQL file: {file_path}")
            return True
            
        except Error as e:
            logger.error(f"Error executing SQL file {file_path}: {e}")
            return False
        except FileNotFoundError:
            logger.error(f"SQL file not found: {file_path}")
            return False
    
    def initialize_database(self) -> bool:
        """Initialize database with schema and sample data"""
        try:
            # Create database
            if not self.create_database():
                return False
            
            # Execute schema
            schema_path = os.path.join(os.path.dirname(__file__), 'schema.sql')
            if not self.execute_sql_file(schema_path):
                return False
            
            logger.info("Database initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            return False
    
    def get_table_count(self, table_name: str) -> Optional[int]:
        """Get count of records in a table"""
        try:
            with self.engine.connect() as connection:
                result = connection.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                count = result.scalar()
                return count
        except SQLAlchemyError as e:
            logger.error(f"Error getting count for table {table_name}: {e}")
            return None
    
    def get_database_stats(self) -> dict:
        """Get database statistics"""
        tables = ['users', 'products', 'categories', 'user_interactions', 'recommendations']
        stats = {}
        
        for table in tables:
            count = self.get_table_count(table)
            stats[table] = count if count is not None else 0
        
        return stats


# Global database manager instance
db_manager = DatabaseManager()


def get_db():
    """Dependency to get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


if __name__ == "__main__":
    # Test database connection and initialization
    print("Testing database connection...")
    
    if db_manager.test_connection():
        print("✅ Database connection successful")
    else:
        print("❌ Database connection failed")
    
    print("\nInitializing database...")
    if db_manager.initialize_database():
        print("✅ Database initialization successful")
        
        # Show database stats
        stats = db_manager.get_database_stats()
        print("\nDatabase Statistics:")
        for table, count in stats.items():
            print(f"  {table}: {count} records")
    else:
        print("❌ Database initialization failed")
