-- Recommendation System Database Schema

-- Create database
CREATE DATABASE IF NOT EXISTS recommendation_db;
USE recommendation_db;

-- Users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON>HA<PERSON>(50),
    last_name <PERSON><PERSON><PERSON><PERSON>(50),
    age INT,
    gender ENUM('M', 'F', 'Other'),
    location VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_username (username),
    INDEX idx_email (email)
);

-- Categories table
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    description TEXT,
    parent_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_name (name),
    INDEX idx_parent (parent_id)
);

-- Products table
CREATE TABLE products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category_id INT,
    price DECIMAL(10, 2) NOT NULL,
    brand VARCHAR(100),
    image_url VARCHAR(500),
    stock_quantity INT DEFAULT 0,
    rating DECIMAL(3, 2) DEFAULT 0.0,
    review_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_name (name),
    INDEX idx_category (category_id),
    INDEX idx_price (price),
    INDEX idx_rating (rating),
    INDEX idx_brand (brand)
);

-- Product features for content-based filtering
CREATE TABLE product_features (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    feature_name VARCHAR(100) NOT NULL,
    feature_value VARCHAR(200),
    feature_type ENUM('categorical', 'numerical', 'text') DEFAULT 'categorical',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_product_feature (product_id, feature_name),
    UNIQUE KEY unique_product_feature (product_id, feature_name)
);

-- User interactions (views, clicks, purchases)
CREATE TABLE user_interactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    interaction_type ENUM('view', 'click', 'add_to_cart', 'purchase', 'like', 'dislike') NOT NULL,
    rating DECIMAL(3, 2) NULL, -- For explicit ratings
    quantity INT DEFAULT 1, -- For purchases
    session_id VARCHAR(100),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_user_product (user_id, product_id),
    INDEX idx_user_time (user_id, timestamp),
    INDEX idx_product_time (product_id, timestamp),
    INDEX idx_interaction_type (interaction_type),
    INDEX idx_session (session_id)
);

-- User preferences (explicit preferences)
CREATE TABLE user_preferences (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    category_id INT,
    brand VARCHAR(100),
    price_min DECIMAL(10, 2),
    price_max DECIMAL(10, 2),
    preference_score DECIMAL(3, 2) DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_user_category (user_id, category_id)
);

-- Recommendations cache
CREATE TABLE recommendations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    score DECIMAL(5, 4) NOT NULL,
    model_type ENUM('collaborative', 'content_based', 'hybrid') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_user_score (user_id, score DESC),
    INDEX idx_expires (expires_at),
    UNIQUE KEY unique_user_product_model (user_id, product_id, model_type)
);

-- Model training logs
CREATE TABLE model_training_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    model_type VARCHAR(50) NOT NULL,
    training_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    training_end TIMESTAMP NULL,
    status ENUM('running', 'completed', 'failed') DEFAULT 'running',
    metrics JSON,
    model_path VARCHAR(500),
    notes TEXT,
    INDEX idx_model_type (model_type),
    INDEX idx_status (status),
    INDEX idx_training_start (training_start)
);
