version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: recommendation_mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: recommendation_db
      MYSQL_USER: appuser
      MYSQL_PASSWORD: apppassword
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - recommendation_network

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: recommendation_redis
    ports:
      - "6379:6379"
    networks:
      - recommendation_network

  # API Backend
  api:
    build: .
    container_name: recommendation_api
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=recommendation_db
      - DB_USER=appuser
      - DB_PASSWORD=apppassword
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    ports:
      - "8000:8000"
    depends_on:
      - mysql
      - redis
    volumes:
      - ./models:/app/models
      - ./logs:/app/logs
    networks:
      - recommendation_network

  # Frontend (optional, can be served separately)
  frontend:
    build: ./frontend
    container_name: recommendation_frontend
    ports:
      - "3000:3000"
    depends_on:
      - api
    networks:
      - recommendation_network

volumes:
  mysql_data:

networks:
  recommendation_network:
    driver: bridge
