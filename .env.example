# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=recommendation_db
DB_USER=root
DB_PASSWORD=your_password

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# ML Model Configuration
MODEL_PATH=models/
RETRAIN_INTERVAL_HOURS=24

# Cloud Configuration (AWS)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
S3_BUCKET=your-recommendation-bucket

# Cloud Configuration (GCP)
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json
GCP_PROJECT_ID=your-project-id
GCS_BUCKET=your-recommendation-bucket

# Redis Configuration (for caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Development
DEBUG=True
RELOAD=True
