-- Initialize database with sample data

-- Source the schema
SOURCE /docker-entrypoint-initdb.d/schema.sql;

-- Insert sample categories
INSERT INTO categories (name, description, parent_id) VALUES
('Electronics', 'Electronic devices and gadgets', NULL),
('Clothing', 'Fashion and apparel', NULL),
('Books', 'Books and literature', NULL),
('Home & Garden', 'Home improvement and gardening', NULL),
('Sports', 'Sports and outdoor activities', NULL);

INSERT INTO categories (name, description, parent_id) VALUES
('Smartphones', 'Mobile phones and accessories', 1),
('Laptops', 'Portable computers', 1),
('Headphones', 'Audio devices', 1),
('Men Clothing', 'Clothing for men', 2),
('Women Clothing', 'Clothing for women', 2),
('Fiction', 'Fiction books', 3),
('Non-Fiction', 'Non-fiction books', 3);

-- Insert sample products
INSERT INTO products (name, description, category_id, price, brand, rating, review_count) VALUES
('iPhone 15 Pro', 'Latest Apple smartphone with advanced features', 6, 999.99, 'Apple', 4.5, 1250),
('Samsung Galaxy S24', 'Premium Android smartphone', 6, 899.99, 'Samsung', 4.3, 980),
('MacBook Pro M3', 'Professional laptop for creative work', 7, 1999.99, 'Apple', 4.7, 750),
('Dell XPS 13', 'Ultrabook for business and personal use', 7, 1299.99, 'Dell', 4.4, 650),
('Sony WH-1000XM5', 'Noise-canceling wireless headphones', 8, 399.99, 'Sony', 4.6, 2100),
('AirPods Pro', 'Apple wireless earbuds with ANC', 8, 249.99, 'Apple', 4.4, 1800),
('Nike Air Max 270', 'Comfortable running shoes', 10, 129.99, 'Nike', 4.2, 890),
('Adidas Ultraboost 22', 'Performance running shoes', 10, 179.99, 'Adidas', 4.5, 720),
('Levi\'s 501 Jeans', 'Classic straight-leg jeans', 9, 79.99, 'Levi\'s', 4.1, 1200),
('The Great Gatsby', 'Classic American novel', 11, 12.99, 'Scribner', 4.3, 5600);

-- Insert sample users
INSERT INTO users (username, email, password_hash, first_name, last_name, age, gender, location) VALUES
('john_doe', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'John', 'Doe', 28, 'M', 'New York'),
('jane_smith', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'Jane', 'Smith', 32, 'F', 'Los Angeles'),
('mike_wilson', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'Mike', 'Wilson', 25, 'M', 'Chicago'),
('sarah_brown', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'Sarah', 'Brown', 29, 'F', 'Houston'),
('alex_johnson', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'Alex', 'Johnson', 35, 'Other', 'Seattle');

-- Insert sample product features
INSERT INTO product_features (product_id, feature_name, feature_value, feature_type) VALUES
(1, 'screen_size', '6.1', 'numerical'),
(1, 'storage', '128GB', 'categorical'),
(1, 'color', 'Space Black', 'categorical'),
(1, 'operating_system', 'iOS', 'categorical'),
(2, 'screen_size', '6.2', 'numerical'),
(2, 'storage', '256GB', 'categorical'),
(2, 'color', 'Phantom Black', 'categorical'),
(2, 'operating_system', 'Android', 'categorical'),
(3, 'screen_size', '14', 'numerical'),
(3, 'ram', '16GB', 'categorical'),
(3, 'storage', '512GB SSD', 'categorical'),
(3, 'processor', 'M3 Pro', 'categorical'),
(4, 'screen_size', '13.4', 'numerical'),
(4, 'ram', '16GB', 'categorical'),
(4, 'storage', '512GB SSD', 'categorical'),
(4, 'processor', 'Intel i7', 'categorical');

-- Insert sample user interactions
INSERT INTO user_interactions (user_id, product_id, interaction_type, rating, quantity, session_id) VALUES
(1, 1, 'view', NULL, 1, 'sess_001'),
(1, 1, 'click', NULL, 1, 'sess_001'),
(1, 1, 'purchase', 5.0, 1, 'sess_001'),
(1, 5, 'view', NULL, 1, 'sess_001'),
(1, 5, 'click', NULL, 1, 'sess_001'),
(2, 2, 'view', NULL, 1, 'sess_002'),
(2, 2, 'purchase', 4.0, 1, 'sess_002'),
(2, 6, 'view', NULL, 1, 'sess_002'),
(2, 7, 'view', NULL, 1, 'sess_003'),
(2, 7, 'add_to_cart', NULL, 1, 'sess_003'),
(3, 3, 'view', NULL, 1, 'sess_004'),
(3, 4, 'view', NULL, 1, 'sess_004'),
(3, 4, 'purchase', 4.5, 1, 'sess_004'),
(4, 9, 'view', NULL, 1, 'sess_005'),
(4, 9, 'purchase', 4.0, 2, 'sess_005'),
(4, 10, 'view', NULL, 1, 'sess_005'),
(5, 5, 'view', NULL, 1, 'sess_006'),
(5, 6, 'view', NULL, 1, 'sess_006'),
(5, 6, 'purchase', 4.5, 1, 'sess_006');
