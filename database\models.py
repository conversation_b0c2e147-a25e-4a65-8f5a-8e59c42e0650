"""
SQLAlchemy models for the recommendation system
"""

from sqlalchemy import Column, Integer, String, Text, DECIMAL, Boolean, DateTime, Enum, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional

Base = declarative_base()


class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    first_name = Column(String(50))
    last_name = Column(String(50))
    age = Column(Integer)
    gender = Column(Enum('M', 'F', 'Other'))
    location = Column(String(100))
    created_at = Column(DateTime, default=func.current_timestamp())
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp())
    is_active = Column(Boolean, default=True)
    
    # Relationships
    interactions = relationship("UserInteraction", back_populates="user")
    preferences = relationship("UserPreference", back_populates="user")
    recommendations = relationship("Recommendation", back_populates="user")


class Category(Base):
    __tablename__ = "categories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text)
    parent_id = Column(Integer, ForeignKey("categories.id"))
    created_at = Column(DateTime, default=func.current_timestamp())
    
    # Relationships
    products = relationship("Product", back_populates="category")
    preferences = relationship("UserPreference", back_populates="category")


class Product(Base):
    __tablename__ = "products"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(200), nullable=False, index=True)
    description = Column(Text)
    category_id = Column(Integer, ForeignKey("categories.id"))
    price = Column(DECIMAL(10, 2), nullable=False, index=True)
    brand = Column(String(100), index=True)
    image_url = Column(String(500))
    stock_quantity = Column(Integer, default=0)
    rating = Column(DECIMAL(3, 2), default=0.0, index=True)
    review_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=func.current_timestamp())
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp())
    is_active = Column(Boolean, default=True)
    
    # Relationships
    category = relationship("Category", back_populates="products")
    features = relationship("ProductFeature", back_populates="product")
    interactions = relationship("UserInteraction", back_populates="product")
    recommendations = relationship("Recommendation", back_populates="product")


class ProductFeature(Base):
    __tablename__ = "product_features"
    
    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    feature_name = Column(String(100), nullable=False)
    feature_value = Column(String(200))
    feature_type = Column(Enum('categorical', 'numerical', 'text'), default='categorical')
    created_at = Column(DateTime, default=func.current_timestamp())
    
    # Relationships
    product = relationship("Product", back_populates="features")


class UserInteraction(Base):
    __tablename__ = "user_interactions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    interaction_type = Column(Enum('view', 'click', 'add_to_cart', 'purchase', 'like', 'dislike'), nullable=False)
    rating = Column(DECIMAL(3, 2))
    quantity = Column(Integer, default=1)
    session_id = Column(String(100))
    timestamp = Column(DateTime, default=func.current_timestamp())
    
    # Relationships
    user = relationship("User", back_populates="interactions")
    product = relationship("Product", back_populates="interactions")


class UserPreference(Base):
    __tablename__ = "user_preferences"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    category_id = Column(Integer, ForeignKey("categories.id"))
    brand = Column(String(100))
    price_min = Column(DECIMAL(10, 2))
    price_max = Column(DECIMAL(10, 2))
    preference_score = Column(DECIMAL(3, 2), default=1.0)
    created_at = Column(DateTime, default=func.current_timestamp())
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp())
    
    # Relationships
    user = relationship("User", back_populates="preferences")
    category = relationship("Category", back_populates="preferences")


class Recommendation(Base):
    __tablename__ = "recommendations"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    score = Column(DECIMAL(5, 4), nullable=False)
    model_type = Column(Enum('collaborative', 'content_based', 'hybrid'), nullable=False)
    created_at = Column(DateTime, default=func.current_timestamp())
    expires_at = Column(DateTime)
    
    # Relationships
    user = relationship("User", back_populates="recommendations")
    product = relationship("Product", back_populates="recommendations")


class ModelTrainingLog(Base):
    __tablename__ = "model_training_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    model_type = Column(String(50), nullable=False)
    training_start = Column(DateTime, default=func.current_timestamp())
    training_end = Column(DateTime)
    status = Column(Enum('running', 'completed', 'failed'), default='running')
    metrics = Column(JSON)
    model_path = Column(String(500))
    notes = Column(Text)
