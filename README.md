# Hệ thống Gợi ý Sản phẩm (E-commerce Recommendation System)

## 📝 Mô tả dự án

Hệ thống gợi ý sản phẩm thông minh cho thương mại điện tử, sử dụng Machine Learning để phân tích hành vi người dùng và đề xuất sản phẩm phù hợp.

## 🏗️ Kiến trúc hệ thống

```
├── data/                   # Dữ liệu và scripts xử lý dữ liệu
├── models/                 # Mô hình ML và training scripts
├── api/                    # FastAPI backend
├── frontend/               # Web interface demo
├── database/               # Database schemas và migrations
├── docker/                 # Docker configurations
├── tests/                  # Unit tests và integration tests
├── docs/                   # Documentation
└── deployment/             # Cloud deployment scripts
```

## 🚀 Công nghệ sử dụng

- **Backend**: Python, FastAPI
- **Machine Learning**: Scikit-learn, TensorFlow/PyTorch
- **Database**: MySQL
- **Frontend**: React/HTML+CSS+JS
- **Cloud**: AWS/GCP (Docker containers)
- **Containerization**: Docker

## 📊 Các loại mô hình gợi ý

1. **Collaborative Filtering**: Dựa trên hành vi người dùng tương tự
2. **Content-based Filtering**: Dựa trên đặc điểm sản phẩm
3. **Hybrid Model**: Kết hợp cả hai phương pháp trên

## 🔧 Cài đặt và chạy

### Yêu cầu hệ thống
- Python 3.8+
- MySQL 8.0+
- Docker (optional)

### Cài đặt dependencies
```bash
pip install -r requirements.txt
```

### Khởi tạo database
```bash
python database/init_db.py
```

### Chạy API server
```bash
uvicorn api.main:app --reload
```

### Chạy frontend
```bash
cd frontend
npm install
npm start
```

## 📈 Tính năng chính

- ✅ Đăng ký/Đăng nhập người dùng
- ✅ Quản lý sản phẩm
- ✅ Theo dõi hành vi người dùng (view, click, purchase)
- ✅ Gợi ý sản phẩm cá nhân hóa
- ✅ API RESTful
- ✅ Web demo interface
- ✅ Cloud deployment ready

## 🧪 Testing

```bash
pytest tests/
```

## 📚 Documentation

Chi tiết tài liệu có trong thư mục `docs/`

## 🚀 Deployment

Xem hướng dẫn deployment trong `deployment/README.md`
